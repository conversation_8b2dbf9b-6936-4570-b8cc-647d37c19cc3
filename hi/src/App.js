import React, { useState } from 'react';
import { Provider, useSelector, useDispatch } from 'react-redux';
import { addItem, deleteItem, editItem, setEditingIndex } from './redux/actions';
import store from './redux/store';
import './App.css';

function CrudComponent() {
  const [input, setInput] = useState('');
  const [editValue, setEditValue] = useState('');
  
  const items = useSelector(state => state.items);
  const editingIndex = useSelector(state => state.editingIndex);
  const dispatch = useDispatch();

  const handleAdd = () => {
    if (input.trim() !== '') {
      dispatch(addItem(input));
      setInput('');
    }
  };

  const handleDelete = (index) => {
    dispatch(deleteItem(index));
  };

  const handleEdit = (index) => {
    dispatch(setEditingIndex(index));
    setEditValue(items[index]);
  };

  const handleUpdate = () => {
    if (editValue.trim() !== '') {
      dispatch(editItem(editingIndex, editValue));
      dispatch(setEditingIndex(null));
      setEditValue('');
    }
  };

  return (
    <div className="CrudApp">
      <h2>Simple Api App</h2>
      <input
        value={input}
        onChange={e => setInput(e.target.value)}
        placeholder="Add new item yoyoyo"
      />
      <button onClick={handleAdd}>Add</button>
      <ul>
        {items.map((item, idx) => (
          <li key={idx}>
            {editingIndex === idx ? (
              <>
                <input
                  value={editValue}
                  onChange={e => setEditValue(e.target.value)}
                />
                <button onClick={handleUpdate}>Update</button>
                <button onClick={() => dispatch(setEditingIndex(null))}>Cancel</button>
              </>
            ) : (
              <>
                {item}
                <button onClick={() => handleEdit(idx)}>Edit</button>
                <button onClick={() => handleDelete(idx)}>Delete</button>
              </>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
}

function App() {
  return (
    <Provider store={store}>
      <CrudComponent />
    </Provider>
  );
}

export default App;
